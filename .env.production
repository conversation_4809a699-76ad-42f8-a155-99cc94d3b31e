# BaseFunded Production Environment Configuration

# Base Pay Configuration - MAINNET
VITE_BASE_PAY_TESTNET=false

# Platform Configuration
VITE_PLATFORM_NAME="BaseFunded"
VITE_PLATFORM_DESCRIPTION="Decentralized Crowdfunding Platform"
VITE_DEFAULT_CONTRIBUTION_AMOUNT=10

# Production Settings
VITE_ENVIRONMENT=production

# Analytics (Optional)
# VITE_GOOGLE_ANALYTICS_ID=G-XXXXXXXXXX
# VITE_MIXPANEL_TOKEN=your_mixpanel_token

# Error Tracking (Optional)
# VITE_SENTRY_DSN=your_sentry_dsn

# API Configuration (if you add a backend)
# VITE_API_BASE_URL=https://api.basefunded.com
# VITE_API_VERSION=v1
