<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, viewport-fit=cover" />
    <meta name="mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-status-bar-style" content="black" />
    <meta name="msapplication-navbutton-color" content="#000000" />
    <meta name="msapplication-TileColor" content="#000000" />
    <title>BaseFunded - Blockchain Crowdfunding Platform</title>
    <meta name="description" content="Create and support campaigns with secure USDC payments on Base blockchain. Transparent, decentralized crowdfunding for good causes." />
    <meta name="author" content="FundMe" />
    <meta name="keywords" content="crowdfunding, blockchain, Base, USDC, campaigns, donations, crypto, decentralized" />

    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="/favicon.ico" />
    <link rel="icon" type="image/jpeg" href="/basepay.JPG" />
    <link rel="apple-touch-icon" href="/basepay.JPG" />
    <link rel="shortcut icon" href="/favicon.ico" />

    <!-- Open Graph / Facebook -->
    <meta property="og:title" content="FundMe Blockchain Crowdfunding Platform" />
    <meta property="og:description" content="Create and support campaigns with secure USDC payments on Base blockchain. Transparent, decentralized crowdfunding for good causes." />
    <meta property="og:type" content="website" />
    <meta property="og:image" content="/basepay.JPG" />
    <meta property="og:url" content="https://basefunded.app" />

    <!-- Twitter -->
    <meta name="twitter:card" content="summary_large_image" />
    <meta name="twitter:title" content="FundMe Blockchain Crowdfunding Platform" />
    <meta name="twitter:description" content="Create and support campaigns with secure USDC payments on Base blockchain." />
    <meta name="twitter:image" content="/basepay.JPG" />

    <!-- Theme Color -->
    <meta name="theme-color" content="#000000" />
    <meta name="theme-color" media="(prefers-color-scheme: dark)" content="#000000" />
    <meta name="theme-color" media="(prefers-color-scheme: light)" content="#000000" />

    <!-- PWA Manifest -->
    <link rel="manifest" href="/manifest.json" />
  </head>

  <body>
    <div id="root"></div>
    <script type="module" src="/src/main.tsx"></script>
  </body>
</html>
