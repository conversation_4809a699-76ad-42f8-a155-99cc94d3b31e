/* Global Dark Theme Override - Forces everything dark */

/* Force dark background on all pages */
body, html, #root {
  background: #000000 !important;
  color: #ffffff !important;
  min-height: 100vh !important;
  min-height: 100dvh !important; /* Dynamic viewport height for mobile */
  overscroll-behavior: none !important; /* Prevent overscroll bounce */
  overscroll-behavior-y: none !important;
  -webkit-overflow-scrolling: touch !important;
}

/* Override all white backgrounds */
div, main, section, article, header, footer, nav {
  background-color: transparent !important;
}

/* Force dark backgrounds on cards and containers */
.bg-white,
.bg-gray-50,
.bg-gray-100,
[class*="bg-white"],
[class*="bg-gray-50"],
[class*="bg-gray-100"] {
  background: #1a1a1a !important;
  border-color: #333333 !important;
}

/* Fix popups, modals, and dialogs */
[role="dialog"],
[role="alertdialog"],
.modal,
.popup,
.toast,
.dropdown,
[data-radix-popper-content-wrapper],
[data-radix-portal],
[data-state="open"] {
  background: #1a1a1a !important;
  background-color: #1a1a1a !important;
  border: 1px solid #333333 !important;
  color: #ffffff !important;
}

/* Fix toast notifications */
.toaster,
.toast,
[class*="toast"] {
  background: #1a1a1a !important;
  background-color: #1a1a1a !important;
  border: 1px solid #333333 !important;
  color: #ffffff !important;
}

/* Force dark text */
.text-gray-900,
.text-gray-800,
.text-gray-700,
.text-black,
[class*="text-gray-9"],
[class*="text-gray-8"],
[class*="text-gray-7"],
[class*="text-black"] {
  color: #ffffff !important;
}

/* Force lighter text for secondary content */
.text-gray-600,
.text-gray-500,
.text-gray-400,
[class*="text-gray-6"],
[class*="text-gray-5"],
[class*="text-gray-4"] {
  color: #a3a3a3 !important;
}

/* Force dark borders */
.border-gray-100,
.border-gray-200,
.border-gray-300,
[class*="border-gray-1"],
[class*="border-gray-2"],
[class*="border-gray-3"] {
  border-color: #333333 !important;
}

/* Force dark input fields */
input, textarea, select {
  background: #1a1a1a !important;
  color: #ffffff !important;
  border-color: #333333 !important;
}

input::placeholder,
textarea::placeholder {
  color: #666666 !important;
}

/* Force dark buttons - but NOT outline buttons */
button:not([data-variant="outline"]):not(.outline-button) {
  background: #333333 !important;
  color: #ffffff !important;
  border: none !important;
}

/* Specifically override outline buttons with higher specificity */
div button[data-variant="outline"],
div .outline-button,
div button.outline-button,
.w-full.outline-button {
  background: transparent !important;
  background-color: transparent !important;
  background-image: none !important;
  border: 2px solid #666666 !important;
  color: #ffffff !important;
}

/* Override for Base Pay button - keep gradient */
button[class*="gradient"],
.base-pay-button {
  background: linear-gradient(to right, #2563eb, #7c3aed) !important;
  background-image: linear-gradient(to right, #2563eb, #7c3aed) !important;
}

/* Override for Sign Up button - make it white/outline */
button[data-state="active"][role="tab"]:last-child,
form[data-signup] button[type="submit"],
.sign-up-button {
  background: transparent !important;
  border: 2px solid #333333 !important;
  color: #ffffff !important;
}

/* Keep Sign In buttons with gradient */
button[data-state="active"][role="tab"]:first-child,
form[data-signin] button[type="submit"],
.sign-in-button {
  background: linear-gradient(to right, #2563eb, #7c3aed) !important;
  background-image: linear-gradient(to right, #2563eb, #7c3aed) !important;
  color: white !important;
}

/* Force dark cards */
.shadow-sm,
.shadow,
.shadow-md,
.shadow-lg {
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.3) !important;
}

/* Progress bars */
.bg-blue-600,
.bg-blue-500,
[class*="bg-blue-"] {
  background: linear-gradient(90deg, #3b82f6, #8b5cf6) !important;
}

/* Ensure readability */
h1, h2, h3, h4, h5, h6 {
  color: #ffffff !important;
}

p {
  color: #d1d5db !important;
}

/* Loading screens */
.bg-gradient-to-br {
  background: linear-gradient(135deg, #000000, #1a1a1a, #000000) !important;
}

/* Override any remaining light elements */
* {
  scrollbar-width: thin;
  scrollbar-color: #333333 #000000;
}

*::-webkit-scrollbar {
  width: 8px;
}

*::-webkit-scrollbar-track {
  background: #000000;
}

*::-webkit-scrollbar-thumb {
  background: #333333;
  border-radius: 4px;
}

*::-webkit-scrollbar-thumb:hover {
  background: #555555;
}

/* Animated background for depth */
body::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: 
    radial-gradient(circle at 20% 20%, rgba(59, 130, 246, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 80%, rgba(139, 92, 246, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 40% 60%, rgba(236, 72, 153, 0.1) 0%, transparent 50%);
  pointer-events: none;
  z-index: -1;
}

/* Ensure all text is visible */
span, div, p, a, label {
  color: inherit !important;
}

/* Fix any remaining white text on white background issues */
.text-white {
  color: #ffffff !important;
}

/* Mobile specific fixes - NO BLUE BACKGROUND */
@media (max-width: 768px) {
  body, html, #root {
    background: #000000 !important;
    background-color: #000000 !important;
    min-height: 100vh !important;
    min-height: 100dvh !important;
    height: 100% !important;
    overscroll-behavior: none !important;
    overscroll-behavior-y: none !important;
    overscroll-behavior-x: none !important;
  }

  /* Fix mobile viewport issues */
  html {
    height: -webkit-fill-available !important;
    background: #000000 !important;
    overscroll-behavior: none !important;
  }

  body {
    min-height: -webkit-fill-available !important;
    position: relative !important;
    overflow-x: hidden !important;
    overscroll-behavior: none !important;
    overscroll-behavior-y: none !important;
    background: #000000 !important;
  }

  #root {
    min-height: -webkit-fill-available !important;
    position: relative !important;
  }

  /* Remove any blue backgrounds on mobile */
  * {
    background-color: inherit !important;
  }

  /* Ensure containers stay dark on mobile */
  div, main, section, article {
    background: transparent !important;
    background-color: transparent !important;
  }

  /* Fix mobile popups and modals */
  [role="dialog"],
  [role="alertdialog"],
  .modal,
  .popup,
  .toast,
  .dropdown,
  [data-radix-popper-content-wrapper],
  [data-radix-portal],
  [data-state="open"] {
    background: #1a1a1a !important;
    background-color: #1a1a1a !important;
    border: 2px solid #333333 !important;
    color: #ffffff !important;
    opacity: 1 !important;
  }

  /* Fix mobile toast notifications */
  .toaster,
  .toast,
  [class*="toast"] {
    background: #1a1a1a !important;
    background-color: #1a1a1a !important;
    border: 2px solid #333333 !important;
    color: #ffffff !important;
    opacity: 1 !important;
  }

  /* Fix mobile browser chrome */
  @supports (-webkit-touch-callout: none) {
    body {
      min-height: -webkit-fill-available !important;
      background: #000000 !important;
      overscroll-behavior: none !important;
    }
  }

  /* Prevent blue overscroll glow on iOS */
  * {
    -webkit-overflow-scrolling: touch !important;
    overscroll-behavior: none !important;
  }

  /* Remove any blue glow effects */
  *::-webkit-scrollbar {
    background: #000000 !important;
  }

  /* Prevent pull-to-refresh blue background */
  html, body {
    overscroll-behavior-y: contain !important;
    background-attachment: fixed !important;
  }
}
